{
    "extends": "../../tsconfig.options.json",
    "compilerOptions": {
        "rootDir": "./src",
        "outDir": "./build",
        "target": "ES2022",
        "lib": ["es2021"],
        "module": "NodeNext",
        "moduleResolution": "NodeNext",
        "strict": true,
        "esModuleInterop": true,
        "skipLibCheck": true,
        "forceConsistentCasingInFileNames": true,
        "sourceRoot": "/",
        // "preserveShebang": true,
        "sourceMap": true,
        "baseUrl": "./src",
        "paths": {
            "@config": ["./shared/config.ts"]
        }
    },
    "references": [
        { "path": "../../packages/malou-package-models" },
        { "path": "../../packages/malou-utils" },
        { "path": "../../packages/malou-dto" },
        { "path": "../../packages/malou-emails" },
        { "path": "../../packages/crawlers" }
    ],
    "include": ["src/**/*", "*.ts"]
}
