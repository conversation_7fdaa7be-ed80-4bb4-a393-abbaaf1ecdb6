#!/usr/bin/env node

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { registerCreatePostTool } from './tools/create-post.js';
import { registerGetMediasTool } from './tools/get-medias.js';
import { registerGetReviewsTool } from './tools/get-reviews.js';
import { registerPreparePostTool } from './tools/prepare-post.js';
import z from 'zod';

// Create server instance
const server = new McpServer({
    name: 'malou-api',
    version: '1.0.0',
    capabilities: {
        resources: {},
        tools: {},
    },
});

server.tool(
    "echo",
    "Echo back a string you send to it.",
    z.object({
      message: z.string().describe("The message to echo back"),
    }),
    async ({ message }) => {
      return {
        content: [
          { type: "text", text: `You said: ${message}` },
        ],
      };
    }
  );

// registerGetReviewsTool(server);
// registerPreparePostTool(server);
// registerCreatePostTool(server);
// registerGetMediasTool(server);

async function main() {
    const transport = new StdioServerTransport();
    await server.connect(transport);
    console.error('Malou MCP Server running on stdio');
}

main().catch((error) => {
    console.error('Fatal error in main():', error);
    process.exit(1);
});
